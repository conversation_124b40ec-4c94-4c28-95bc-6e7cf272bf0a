"""
请求参数验证器
"""
from marshmallow import Schema, fields, validate, ValidationError
from typing import List, Optional


class PaginationSchema(Schema):
    """分页参数验证"""
    page = fields.Integer(missing=1, validate=validate.Range(min=1))
    page_size = fields.Integer(missing=10, validate=validate.Range(min=1, max=100))
    from_offset = fields.Integer(missing=None, validate=validate.Range(min=0))
    size = fields.Integer(missing=None, validate=validate.Range(min=1, max=100))


class FilterSchema(Schema):
    """基础过滤参数验证"""
    kkxy_code = fields.String(missing=None, allow_none=True)  # 学院代码
    term_ids = fields.List(fields.String(), missing=None, allow_none=True)  # 学期ID列表
    xueke = fields.String(missing=None, allow_none=True)  # 学科名称
    start_time = fields.DateTime(missing=None, allow_none=True)  # 起始时间
    end_time = fields.DateTime(missing=None, allow_none=True)  # 结束时间


class SearchSchema(Schema):
    """搜索参数验证"""
    query = fields.String(required=True, validate=validate.Length(min=1, max=500))  # 搜索关键词
    search_words = fields.List(fields.String(), missing=None, allow_none=True)  # 内容搜索关键字
    content_biz_labels = fields.List(fields.String(), missing=None, allow_none=True)  # 业务关键词
    content_entity_labels = fields.List(fields.String(), missing=None, allow_none=True)  # 实体标签


class VectorSearchSchema(Schema):
    """向量搜索参数验证"""
    query_vector = fields.List(fields.Float(), missing=None, allow_none=True)  # 查询向量
    k = fields.Integer(missing=50, validate=validate.Range(min=1, max=100))  # KNN 的 K 值
    num_candidates = fields.Integer(missing=100, validate=validate.Range(min=1, max=1000))  # 候选数量


class SortSchema(Schema):
    """排序参数验证"""
    sort_field = fields.String(missing="_score")  # 排序字段
    sort_order = fields.String(missing="desc", validate=validate.OneOf(["asc", "desc"]))  # 排序方向


class HighlightSchema(Schema):
    """高亮参数验证"""
    highlight_fields = fields.List(fields.String(), missing=["title", "realname", "asr_data"])  # 高亮字段
    pre_tags = fields.List(fields.String(), missing=["<em>"])  # 高亮前缀标签
    post_tags = fields.List(fields.String(), missing=["</em>"])  # 高亮后缀标签


def validate_request_data(schema_class: Schema, data: dict) -> dict:
    """验证请求数据"""
    try:
        schema = schema_class()
        return schema.load(data)
    except ValidationError as e:
        raise ValueError(f"参数验证失败: {e.messages}")


def validate_group_year(group_id: str, year: str) -> tuple:
    """验证组织ID和年份参数"""
    if not group_id or not group_id.strip():
        raise ValueError("group_id 参数不能为空")
    
    if not year or not year.strip():
        raise ValueError("year 参数不能为空")
    
    try:
        year_int = int(year)
        if year_int < 2000 or year_int > 2100:
            raise ValueError("year 参数必须是有效的年份")
    except ValueError:
        raise ValueError("year 参数必须是数字")
    
    return group_id.strip(), year.strip()
