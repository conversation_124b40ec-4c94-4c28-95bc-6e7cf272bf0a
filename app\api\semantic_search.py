"""
语义检索接口
"""
import logging
from flask import request, current_app
from flask_restx import Namespace, Resource, fields
from app.services.es_service import es_service
from app.services.query_builder import SemanticQueryBuilder
from app.models.es_models import FilterParams, SearchParams, VectorParams, PaginationParams, HighlightParams
from app.utils.response import success_response, error_response, paginated_response
from app.utils.validators import validate_group_year, validate_request_data, PaginationSchema

# 创建命名空间
ns = Namespace('semantic', description='语义检索接口')

# 定义请求模型
filter_model = ns.model('FilterParams', {
    'kkxy_code': fields.String(description='学院代码'),
    'term_ids': fields.List(fields.String, description='学期ID列表'),
    'xueke': fields.String(description='学科名称'),
    'start_time': fields.String(description='起始时间'),
    'end_time': fields.String(description='结束时间')
})

search_model = ns.model('SearchParams', {
    'query': fields.String(required=True, description='搜索关键词'),
    'search_words': fields.List(fields.String, description='内容搜索关键字'),
    'content_biz_labels': fields.List(fields.String, description='业务关键词'),
    'content_entity_labels': fields.List(fields.String, description='实体标签')
})

vector_model = ns.model('VectorParams', {
    'query_vector': fields.List(fields.Float, description='查询向量'),
    'k': fields.Integer(description='KNN的K值', default=50),
    'num_candidates': fields.Integer(description='候选数量', default=100),
    'boost': fields.Float(description='权重提升', default=3.0)
})

pagination_model = ns.model('PaginationParams', {
    'from_offset': fields.Integer(description='偏移量', default=0),
    'size': fields.Integer(description='返回数量', default=10)
})

course_request_model = ns.model('CourseSearchRequest', {
    'group_id': fields.String(required=True, description='组织ID'),
    'year': fields.String(required=True, description='年份'),
    'filter': fields.Nested(filter_model, description='过滤参数'),
    'search': fields.Nested(search_model, required=True, description='搜索参数'),
    'vector': fields.Nested(vector_model, description='向量搜索参数'),
    'pagination': fields.Nested(pagination_model, description='分页参数')
})

# 定义响应模型
search_result_model = ns.model('SearchResult', {
    'id': fields.String(description='文档ID'),
    'score': fields.Float(description='相关性得分'),
    'source': fields.Raw(description='文档内容'),
    'highlight': fields.Raw(description='高亮内容')
})

search_response_model = ns.model('SearchResponse', {
    'total': fields.Integer(description='总数量'),
    'max_score': fields.Float(description='最高得分'),
    'hits': fields.List(fields.Nested(search_result_model), description='搜索结果'),
    'aggregations': fields.Raw(description='聚合结果'),
    'took': fields.Integer(description='查询耗时(ms)')
})


@ns.route('/courses')
class CourseSemanticSearch(Resource):
    """在线课程语义检索"""
    
    @ns.doc('search_courses')
    @ns.expect(course_request_model)
    @ns.marshal_with(search_response_model)
    def post(self):
        """
        在线课程语义检索
        
        支持以下检索功能：
        1. 基础过滤：学院、学期、学科、时间范围
        2. 文本搜索：课程名称、教师姓名、ASR数据等多字段匹配
        3. 向量检索：基于文本段嵌入的语义相似度搜索
        4. 聚合统计：按课程维度分组统计
        5. 高亮显示：搜索关键词高亮
        """
        try:
            data = request.get_json()
            if not data:
                return error_response(400, "请求体不能为空")
            
            # 验证必需参数
            group_id = data.get('group_id')
            year = data.get('year')
            validate_group_year(group_id, year)
            
            # 构建索引名称
            index_name = f"{current_app.config['ES_INDEX_PREFIX']}_{group_id}_{year}"
            
            # 检查索引是否存在
            if not es_service.index_exists(index_name):
                return error_response(404, f"索引 {index_name} 不存在")
            
            # 解析请求参数
            filter_data = data.get('filter', {})
            search_data = data.get('search', {})
            vector_data = data.get('vector', {})
            pagination_data = data.get('pagination', {})
            
            # 验证搜索参数
            if not search_data.get('query'):
                return error_response(400, "搜索关键词不能为空")
            
            # 构建查询参数
            filter_params = FilterParams(
                kkxy_code=filter_data.get('kkxy_code'),
                term_ids=filter_data.get('term_ids'),
                xueke=filter_data.get('xueke'),
                start_time=filter_data.get('start_time'),
                end_time=filter_data.get('end_time')
            )
            
            search_params = SearchParams(
                query=search_data['query'],
                search_words=search_data.get('search_words'),
                content_biz_labels=search_data.get('content_biz_labels'),
                content_entity_labels=search_data.get('content_entity_labels')
            )
            
            vector_params = None
            if vector_data.get('query_vector'):
                vector_params = VectorParams(
                    query_vector=vector_data['query_vector'],
                    k=vector_data.get('k', 50),
                    num_candidates=vector_data.get('num_candidates', 100),
                    boost=vector_data.get('boost', 3.0)
                )
            
            pagination_params = PaginationParams(
                from_offset=pagination_data.get('from_offset', 0),
                size=pagination_data.get('size', 10)
            )
            
            highlight_params = HighlightParams()
            
            # 构建查询
            query_builder = SemanticQueryBuilder()
            query = query_builder.build_course_query(
                group_id, year, filter_params, search_params, 
                vector_params, pagination_params, highlight_params
            )
            
            # 执行搜索
            result = es_service.search(index_name, query)
            
            # 格式化响应
            response_data = {
                'total': result.total,
                'max_score': result.max_score,
                'hits': [
                    {
                        'id': hit.id,
                        'score': hit.score,
                        'source': hit.source,
                        'highlight': hit.highlight
                    }
                    for hit in result.hits
                ],
                'aggregations': result.aggregations,
                'took': result.took
            }
            
            return success_response(response_data, "在线课程检索成功")
            
        except ValueError as e:
            return error_response(400, str(e))
        except Exception as e:
            current_app.logger.error(f"在线课程检索失败: {str(e)}", exc_info=True)
            return error_response(500, "检索服务异常", str(e))


@ns.route('/resources')
class ResourceSemanticSearch(Resource):
    """辅学资源语义检索"""
    
    @ns.doc('search_resources')
    @ns.expect(course_request_model)
    @ns.marshal_with(search_response_model)
    def post(self):
        """
        辅学资源语义检索
        
        支持以下检索功能：
        1. 基础过滤：学院、学期、学科、时间范围
        2. 文本搜索：资源名称、内容关键字等
        3. 向量检索：基于文本段嵌入的语义相似度搜索
        4. 高亮显示：搜索关键词高亮
        """
        try:
            data = request.get_json()
            if not data:
                return error_response(400, "请求体不能为空")
            
            # 验证必需参数
            group_id = data.get('group_id')
            year = data.get('year')
            validate_group_year(group_id, year)
            
            # 构建索引名称
            index_name = f"{current_app.config['ES_INDEX_PREFIX']}_{group_id}_{year}"
            
            # 检查索引是否存在
            if not es_service.index_exists(index_name):
                return error_response(404, f"索引 {index_name} 不存在")
            
            # 解析请求参数
            filter_data = data.get('filter', {})
            search_data = data.get('search', {})
            vector_data = data.get('vector', {})
            pagination_data = data.get('pagination', {})
            
            # 验证搜索参数
            if not search_data.get('query'):
                return error_response(400, "搜索关键词不能为空")
            
            # 构建查询参数（辅学资源：entity_type=0, category=2）
            filter_params = FilterParams(
                entity_type=0,
                category=2,
                kkxy_code=filter_data.get('kkxy_code'),
                term_ids=filter_data.get('term_ids'),
                xueke=filter_data.get('xueke'),
                start_time=filter_data.get('start_time'),
                end_time=filter_data.get('end_time')
            )
            
            search_params = SearchParams(
                query=search_data['query'],
                search_words=search_data.get('search_words'),
                content_biz_labels=search_data.get('content_biz_labels'),
                content_entity_labels=search_data.get('content_entity_labels')
            )
            
            vector_params = None
            if vector_data.get('query_vector'):
                vector_params = VectorParams(
                    query_vector=vector_data['query_vector'],
                    k=vector_data.get('k', 50),
                    num_candidates=vector_data.get('num_candidates', 100),
                    boost=vector_data.get('boost', 3.0)
                )
            
            pagination_params = PaginationParams(
                from_offset=pagination_data.get('from_offset', 0),
                size=pagination_data.get('size', 10)
            )
            
            highlight_params = HighlightParams()
            
            # 构建查询
            query_builder = SemanticQueryBuilder()
            query = query_builder.build_course_query(
                group_id, year, filter_params, search_params, 
                vector_params, pagination_params, highlight_params
            )
            
            # 执行搜索
            result = es_service.search(index_name, query)
            
            # 格式化响应
            response_data = {
                'total': result.total,
                'max_score': result.max_score,
                'hits': [
                    {
                        'id': hit.id,
                        'score': hit.score,
                        'source': hit.source,
                        'highlight': hit.highlight
                    }
                    for hit in result.hits
                ],
                'aggregations': result.aggregations,
                'took': result.took
            }
            
            return success_response(response_data, "辅学资源检索成功")
            
        except ValueError as e:
            return error_response(400, str(e))
        except Exception as e:
            current_app.logger.error(f"辅学资源检索失败: {str(e)}", exc_info=True)
            return error_response(500, "检索服务异常", str(e))


@ns.route('/graphs')
class GraphSemanticSearch(Resource):
    """图谱资源语义检索"""

    @ns.doc('search_graphs')
    @ns.expect(course_request_model)
    @ns.marshal_with(search_response_model)
    def post(self):
        """
        图谱资源语义检索

        支持以下检索功能：
        1. 基础过滤：学院、学期、学科、时间范围
        2. 文本搜索：图谱名称、内容关键字等
        3. 向量检索：基于文本段嵌入的语义相似度搜索
        4. 高亮显示：搜索关键词高亮
        """
        try:
            data = request.get_json()
            if not data:
                return error_response(400, "请求体不能为空")

            # 验证必需参数
            group_id = data.get('group_id')
            year = data.get('year')
            validate_group_year(group_id, year)

            # 构建索引名称
            index_name = f"{current_app.config['ES_INDEX_PREFIX']}_{group_id}_{year}"

            # 检查索引是否存在
            if not es_service.index_exists(index_name):
                return error_response(404, f"索引 {index_name} 不存在")

            # 解析请求参数
            filter_data = data.get('filter', {})
            search_data = data.get('search', {})
            vector_data = data.get('vector', {})
            pagination_data = data.get('pagination', {})

            # 验证搜索参数
            if not search_data.get('query'):
                return error_response(400, "搜索关键词不能为空")

            # 构建查询参数（图谱资源：entity_type=0, category=3）
            filter_params = FilterParams(
                entity_type=0,
                category=3,
                kkxy_code=filter_data.get('kkxy_code'),
                term_ids=filter_data.get('term_ids'),
                xueke=filter_data.get('xueke'),
                start_time=filter_data.get('start_time'),
                end_time=filter_data.get('end_time')
            )

            search_params = SearchParams(
                query=search_data['query'],
                search_words=search_data.get('search_words'),
                content_biz_labels=search_data.get('content_biz_labels'),
                content_entity_labels=search_data.get('content_entity_labels')
            )

            vector_params = None
            if vector_data.get('query_vector'):
                vector_params = VectorParams(
                    query_vector=vector_data['query_vector'],
                    k=vector_data.get('k', 50),
                    num_candidates=vector_data.get('num_candidates', 100),
                    boost=vector_data.get('boost', 3.0)
                )

            pagination_params = PaginationParams(
                from_offset=pagination_data.get('from_offset', 0),
                size=pagination_data.get('size', 10)
            )

            highlight_params = HighlightParams()

            # 构建查询
            query_builder = SemanticQueryBuilder()
            query = query_builder.build_course_query(
                group_id, year, filter_params, search_params,
                vector_params, pagination_params, highlight_params
            )

            # 执行搜索
            result = es_service.search(index_name, query)

            # 格式化响应
            response_data = {
                'total': result.total,
                'max_score': result.max_score,
                'hits': [
                    {
                        'id': hit.id,
                        'score': hit.score,
                        'source': hit.source,
                        'highlight': hit.highlight
                    }
                    for hit in result.hits
                ],
                'aggregations': result.aggregations,
                'took': result.took
            }

            return success_response(response_data, "图谱资源检索成功")

        except ValueError as e:
            return error_response(400, str(e))
        except Exception as e:
            current_app.logger.error(f"图谱资源检索失败: {str(e)}", exc_info=True)
            return error_response(500, "检索服务异常", str(e))
