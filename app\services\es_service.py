"""
Elasticsearch 连接和基础服务
"""
import logging
from typing import Dict, Any, Optional
from elasticsearch import Elasticsearch
from flask import current_app
from app.models.es_models import SearchResponse


class ESService:
    """Elasticsearch 服务类"""
    
    def __init__(self):
        self._client = None
        self.logger = logging.getLogger(__name__)
    
    @property
    def client(self) -> Elasticsearch:
        """获取 ES 客户端实例（懒加载）"""
        if self._client is None:
            self._client = self._create_client()
        return self._client
    
    def _create_client(self) -> Elasticsearch:
        """创建 ES 客户端"""
        config = current_app.config
        
        # 构建连接配置
        es_config = {
            'hosts': [{'host': config['ES_HOST'], 'port': config['ES_PORT']}],
            'timeout': config['ES_TIMEOUT'],
            'retry_on_timeout': True,
            'max_retries': 3
        }
        
        # 添加认证信息
        if config['ES_USERNAME'] and config['ES_PASSWORD']:
            es_config['http_auth'] = (config['ES_USERNAME'], config['ES_PASSWORD'])
        
        # 添加 SSL 配置
        if config['ES_USE_SSL']:
            es_config['use_ssl'] = True
            es_config['verify_certs'] = config['ES_VERIFY_CERTS']
        
        try:
            client = Elasticsearch(**es_config)
            # 测试连接
            if client.ping():
                self.logger.info("Elasticsearch 连接成功")
                return client
            else:
                raise ConnectionError("无法连接到 Elasticsearch")
        except Exception as e:
            self.logger.error(f"Elasticsearch 连接失败: {str(e)}")
            raise
    
    def search(self, index: str, body: Dict[str, Any]) -> SearchResponse:
        """执行搜索查询"""
        try:
            self.logger.debug(f"执行搜索查询 - 索引: {index}, 查询体: {body}")
            response = self.client.search(index=index, body=body)
            return SearchResponse.from_es_response(response)
        except Exception as e:
            self.logger.error(f"搜索查询失败: {str(e)}")
            raise
    
    def get_document(self, index: str, doc_id: str) -> Optional[Dict[str, Any]]:
        """获取单个文档"""
        try:
            response = self.client.get(index=index, id=doc_id)
            return response['_source']
        except Exception as e:
            self.logger.error(f"获取文档失败: {str(e)}")
            return None
    
    def index_exists(self, index: str) -> bool:
        """检查索引是否存在"""
        try:
            return self.client.indices.exists(index=index)
        except Exception as e:
            self.logger.error(f"检查索引存在性失败: {str(e)}")
            return False
    
    def get_index_info(self, index: str) -> Optional[Dict[str, Any]]:
        """获取索引信息"""
        try:
            if self.index_exists(index):
                stats = self.client.indices.stats(index=index)
                return stats['indices'][index]
            return None
        except Exception as e:
            self.logger.error(f"获取索引信息失败: {str(e)}")
            return None


# 全局 ES 服务实例
es_service = ESService()
