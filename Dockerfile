FROM cmc-tcr.tencentcloudcr.com/yunjiao/filebeat

ARG TARGETARCH
ENV XTARGETARCH=${TARGETARCH:-amd64}

WORKDIR /mcloud/yunjiao

COPY build/docker/docker-entrypoint.d/ /docker-entrypoint.d/
RUN mkdir /docker && \
    ln -s /docker-entrypoint.sh /docker/docker-entrypoint.sh && \
    chmod +x -R /docker-entrypoint.d/

COPY cmd/bin/jyappapi_${XTARGETARCH} jyappapi
COPY configs/config.json.env configs/
COPY private_key .
	
RUN chmod +x -R jyappapi /docker
	
EXPOSE 8085

RUN mkdir -p /go/logs && chmod 755 -R /go/logs

CMD ["/mcloud/yunjiao/jyappapi"]