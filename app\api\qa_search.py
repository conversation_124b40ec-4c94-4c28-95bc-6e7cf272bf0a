"""
AI 问答检索接口
"""
import logging
from flask import request, current_app
from flask_restx import Namespace, Resource, fields
from app.services.es_service import es_service
from app.services.query_builder import KnowledgeQueryBuilder
from app.models.es_models import FilterParams, SearchParams, VectorParams, PaginationParams, SortParams
from app.utils.response import success_response, error_response
from app.utils.validators import validate_group_year

# 创建命名空间
ns = Namespace('qa', description='AI 问答检索接口')

# 定义请求模型
qa_search_model = ns.model('QASearchParams', {
    'search_words': fields.List(fields.String, description='内容搜索关键字'),
    'content_biz_labels': fields.List(fields.String, description='业务关键词'),
    'content_entity_labels': fields.List(fields.String, description='实体标签')
})

qa_vector_model = ns.model('QAVectorParams', {
    'query_vector': fields.List(fields.Float, required=True, description='查询向量'),
    'k': fields.Integer(description='KNN的K值', default=50),
    'num_candidates': fields.Integer(description='候选数量', default=100),
    'boost': fields.Float(description='权重提升', default=3.0)
})

qa_pagination_model = ns.model('QAPaginationParams', {
    'from_offset': fields.Integer(description='偏移量', default=0),
    'size': fields.Integer(description='返回数量', default=20)
})

course_qa_request_model = ns.model('CourseQARequest', {
    'group_id': fields.String(required=True, description='组织ID'),
    'year': fields.String(required=True, description='年份'),
    'kg_ids': fields.List(fields.String, required=True, description='课程知识库ID列表'),
    'search': fields.Nested(qa_search_model, description='搜索参数'),
    'vector': fields.Nested(qa_vector_model, required=True, description='向量搜索参数'),
    'pagination': fields.Nested(qa_pagination_model, description='分页参数')
})

graph_qa_request_model = ns.model('GraphQARequest', {
    'group_id': fields.String(required=True, description='组织ID'),
    'year': fields.String(required=True, description='年份'),
    'kg_ids': fields.List(fields.String, required=True, description='图谱知识库ID列表'),
    'search': fields.Nested(qa_search_model, description='搜索参数'),
    'vector': fields.Nested(qa_vector_model, required=True, description='向量搜索参数'),
    'pagination': fields.Nested(qa_pagination_model, description='分页参数')
})

# 定义响应模型
qa_result_model = ns.model('QAResult', {
    'id': fields.String(description='文档ID'),
    'score': fields.Float(description='相关性得分'),
    'chunck_id': fields.String(description='文段ID'),
    'entity_id': fields.String(description='实体ID')
})

qa_response_model = ns.model('QAResponse', {
    'total': fields.Integer(description='总数量'),
    'max_score': fields.Float(description='最高得分'),
    'hits': fields.List(fields.Nested(qa_result_model), description='搜索结果'),
    'took': fields.Integer(description='查询耗时(ms)')
})


@ns.route('/course')
class CourseQA(Resource):
    """AI 课程问答检索"""
    
    @ns.doc('course_qa_search')
    @ns.expect(course_qa_request_model)
    # @ns.marshal_with(qa_response_model)
    def post(self):
        """
        AI 课程问答检索
        
        支持以下检索功能：
        1. 基于特定课程知识库的检索（entity_type=1, kg_ids=课程知识库id）
        2. 关键词匹配：搜索关键字、业务关键词、实体标签
        3. 向量检索：基于用户query的语义相似度搜索
        4. 返回排名最高的文段，用于问答生成
        """
        try:
            data = request.get_json()
            if not data:
                return error_response(400, "请求体不能为空")
            
            # 验证必需参数
            group_id = data.get('group_id')
            year = data.get('year','*')
            #validate_group_year(group_id, year)
            
            kg_ids = data.get('kg_ids')
            if not kg_ids or not isinstance(kg_ids, list):
                return error_response(400, "kg_ids 必须是非空列表")
            
            # 构建索引名称
            index_name = f"{current_app.config['ES_INDEX_PREFIX']}_{group_id}_{year}"
            
            # 检查索引是否存在
            if not es_service.index_exists(index_name):
                return error_response(404, f"索引 {index_name} 不存在")
            
            # 解析请求参数
            search_data = data.get('search', {})
            vector_data = data.get('vector', {})
            pagination_data = data.get('pagination', {})
            
            # 验证向量参数
            if not vector_data.get('query_vector'):
                return error_response(400, "查询向量不能为空")
            
            # 构建查询参数
            filter_params = FilterParams(
                entity_type=1,
                kg_ids=kg_ids
            )
            
            search_params = SearchParams(
                query="",  # QA检索不需要query字段
                search_words=search_data.get('search_words'),
                content_biz_labels=search_data.get('content_biz_labels'),
                content_entity_labels=search_data.get('content_entity_labels')
            )
            
            vector_params = VectorParams(
                query_vector=vector_data['query_vector'],
                k=vector_data.get('k', 50),
                num_candidates=vector_data.get('num_candidates', 100),
                boost=vector_data.get('boost', 3.0)
            )
            
            pagination_params = PaginationParams(
                from_offset=pagination_data.get('from_offset', 0),
                size=pagination_data.get('size', 20)
            )
            
            # 构建查询
            query_builder = KnowledgeQueryBuilder()
            
            # 添加过滤条件
            query_builder.add_filter(filter_params)
            
            # 添加搜索条件（如果有）
            if search_params.search_words or search_params.content_biz_labels or search_params.content_entity_labels:
                should_queries = []
                
                if search_params.search_words:
                    should_queries.append({
                        "terms": {"search_words": search_params.search_words, "boost": 1}
                    })
                
                if search_params.content_biz_labels:
                    should_queries.append({
                        "terms": {"content_biz_labels": search_params.content_biz_labels, "boost": 1}
                    })
                
                if search_params.content_entity_labels:
                    should_queries.append({
                        "terms": {"content_entity_labels": search_params.content_entity_labels, "boost": 1}
                    })
                
                if should_queries:
                    query_builder.query["query"]["bool"]["should"] = should_queries
                    query_builder.query["query"]["bool"]["minimum_should_match"] = 0
            
            # 添加向量搜索
            query_builder.add_vector_search(vector_params)
            query_builder.add_pagination(pagination_params)
            query_builder.add_source_fields(["chunck_id", "entity_id"])
            query_builder.add_sort(SortParams())
            
            query = query_builder.build()
            
            # 执行搜索
            result = es_service.search(index_name, query)
            
            # 格式化响应
            response_data = {
                'total': result.total,
                'max_score': result.max_score,
                'hits': [
                    {
                        'id': hit.id,
                        'score': hit.score,
                        'chunck_id': hit.source.get('chunck_id'),
                        'entity_id': hit.source.get('entity_id')
                    }
                    for hit in result.hits
                ],
                'took': result.took
            }
            
            return success_response(response_data, "AI课程问答检索成功")
            
        except ValueError as e:
            return error_response(400, str(e))
        except Exception as e:
            current_app.logger.error(f"AI课程问答检索失败: {str(e)}", exc_info=True)
            return error_response(500, "检索服务异常", str(e))


@ns.route('/graph')
class GraphQA(Resource):
    """图谱智能问答检索"""
    
    @ns.doc('graph_qa_search')
    @ns.expect(graph_qa_request_model)
    # @ns.marshal_with(qa_response_model)
    def post(self):
        """
        图谱智能问答检索
        
        支持以下检索功能：
        1. 基于特定图谱知识库的检索（entity_type=1, kg_ids=图谱知识库id）
        2. 关键词匹配：搜索关键字、业务关键词、实体标签
        3. 向量检索：基于用户query的语义相似度搜索
        4. 返回排名最高的文段，用于问答生成
        """
        try:
            data = request.get_json()
            if not data:
                return error_response(400, "请求体不能为空")
            
            # 验证必需参数
            group_id = data.get('group_id')
            year = data.get('year','*')
            #validate_group_year(group_id, year)
            
            kg_ids = data.get('kg_ids')
            if not kg_ids or not isinstance(kg_ids, list):
                return error_response(400, "kg_ids 必须是非空列表")
            
            # 构建索引名称
            index_name = f"{current_app.config['ES_INDEX_PREFIX']}_{group_id}_{year}"
            
            # 检查索引是否存在
            if not es_service.index_exists(index_name):
                return error_response(404, f"索引 {index_name} 不存在")
            
            # 解析请求参数
            search_data = data.get('search', {})
            vector_data = data.get('vector', {})
            pagination_data = data.get('pagination', {})
            
            # 验证向量参数
            if not vector_data.get('query_vector'):
                return error_response(400, "查询向量不能为空")
            
            # 构建查询参数
            filter_params = FilterParams(
                entity_type=1,
                kg_ids=kg_ids
            )
            
            search_params = SearchParams(
                query="",  # QA检索不需要query字段
                search_words=search_data.get('search_words'),
                content_biz_labels=search_data.get('content_biz_labels'),
                content_entity_labels=search_data.get('content_entity_labels')
            )
            
            vector_params = VectorParams(
                query_vector=vector_data['query_vector'],
                k=vector_data.get('k', 50),
                num_candidates=vector_data.get('num_candidates', 100),
                boost=vector_data.get('boost', 3.0)
            )
            
            pagination_params = PaginationParams(
                from_offset=pagination_data.get('from_offset', 0),
                size=pagination_data.get('size', 20)
            )
            
            # 构建查询
            query_builder = KnowledgeQueryBuilder()
            
            # 添加过滤条件
            query_builder.add_filter(filter_params)
            
            # 添加搜索条件（如果有）
            if search_params.search_words or search_params.content_biz_labels or search_params.content_entity_labels:
                should_queries = []
                
                if search_params.search_words:
                    should_queries.append({
                        "terms": {"search_words": search_params.search_words, "boost": 1}
                    })
                
                if search_params.content_biz_labels:
                    should_queries.append({
                        "terms": {"content_biz_labels": search_params.content_biz_labels, "boost": 1}
                    })
                
                if search_params.content_entity_labels:
                    should_queries.append({
                        "terms": {"content_entity_labels": search_params.content_entity_labels, "boost": 1}
                    })
                
                if should_queries:
                    query_builder.query["query"]["bool"]["should"] = should_queries
                    query_builder.query["query"]["bool"]["minimum_should_match"] = 0
            
            # 添加向量搜索
            query_builder.add_vector_search(vector_params)
            query_builder.add_pagination(pagination_params)
            query_builder.add_source_fields(["chunck_id", "entity_id"])
            query_builder.add_sort(SortParams())
            
            query = query_builder.build()
            
            # 执行搜索
            result = es_service.search(index_name, query)
            
            # 格式化响应
            response_data = {
                'total': result.total,
                'max_score': result.max_score,
                'hits': [
                    {
                        'id': hit.id,
                        'score': hit.score,
                        'chunck_id': hit.source.get('chunck_id'),
                        'entity_id': hit.source.get('entity_id')
                    }
                    for hit in result.hits
                ],
                'took': result.took
            }
            
            return success_response(response_data, "图谱智能问答检索成功")
            
        except ValueError as e:
            return error_response(400, str(e))
        except Exception as e:
            current_app.logger.error(f"图谱智能问答检索失败: {str(e)}", exc_info=True)
            return error_response(500, "检索服务异常", str(e))
