"""
知识库检索接口
"""
import logging
from flask import request, current_app
from flask_restx import Namespace, Resource, fields
from app.services.es_service import es_service
from app.services.query_builder import KnowledgeQueryBuilder
from app.models.es_models import SearchParams, VectorParams, PaginationParams
from app.utils.response import success_response, error_response
from app.utils.validators import validate_group_year

# 创建命名空间
ns = Namespace('knowledge', description='知识库检索接口')

# 定义请求模型
knowledge_search_model = ns.model('KnowledgeSearchParams', {
    'search_words': fields.List(fields.String, description='内容搜索关键字'),
    'content_biz_labels': fields.List(fields.String, description='业务关键词'),
    'content_entity_labels': fields.List(fields.String, description='实体标签')
})

knowledge_vector_model = ns.model('KnowledgeVectorParams', {
    'query_vector': fields.List(fields.Float, required=True, description='查询向量'),
    'k': fields.Integer(description='KNN的K值', default=50),
    'num_candidates': fields.Integer(description='候选数量', default=100),
    'boost': fields.Float(description='权重提升', default=3.0)
})

knowledge_pagination_model = ns.model('KnowledgePaginationParams', {
    'from_offset': fields.Integer(description='偏移量', default=0),
    'size': fields.Integer(description='返回数量', default=20)
})

knowledge_request_model = ns.model('KnowledgeSearchRequest', {
    'group_id': fields.String(required=True, description='组织ID'),
    'year': fields.String(required=True, description='年份'),
    'search': fields.Nested(knowledge_search_model, description='搜索参数'),
    'vector': fields.Nested(knowledge_vector_model, required=True, description='向量搜索参数'),
    'pagination': fields.Nested(knowledge_pagination_model, description='分页参数')
})

# 定义响应模型
knowledge_result_model = ns.model('KnowledgeResult', {
    'id': fields.String(description='文档ID'),
    'score': fields.Float(description='相关性得分'),
    'chunck_id': fields.String(description='文段ID'),
    'entity_id': fields.String(description='实体ID')
})

knowledge_response_model = ns.model('KnowledgeResponse', {
    'total': fields.Integer(description='总数量'),
    'max_score': fields.Float(description='最高得分'),
    'hits': fields.List(fields.Nested(knowledge_result_model), description='搜索结果'),
    'took': fields.Integer(description='查询耗时(ms)')
})

chunk_detail_model = ns.model('ChunkDetail', {
    'chunck_id': fields.String(description='文段ID'),
    'content': fields.String(description='文段内容'),
    'entity_id': fields.String(description='实体ID')
})


@ns.route('/search')
class KnowledgeSearch(Resource):
    """知识库检索"""
    
    @ns.doc('search_knowledge')
    @ns.expect(knowledge_request_model)
    # @ns.marshal_with(knowledge_response_model)
    def post(self):
        """
        知识库检索接口
        
        支持以下检索功能：
        1. 实体知识点片段检索（entity_type=1）
        2. 关键词匹配：搜索关键字、业务关键词、实体标签
        3. 向量检索：基于文本段嵌入的语义相似度搜索
        4. 返回文段ID和实体ID，用于后续获取详细内容
        """
        try:
            data = request.get_json()
            if not data:
                return error_response(400, "请求体不能为空")
            
            # 验证必需参数
            group_id = data.get('group_id')
            year = data.get('year','*')
            #validate_group_year(group_id, year)
            
            # 构建索引名称
            index_name = f"{current_app.config['ES_INDEX_PREFIX']}_{group_id}_{year}"
            
            # 检查索引是否存在
            if not es_service.index_exists(index_name):
                return error_response(404, f"索引 {index_name} 不存在")
            
            # 解析请求参数
            search_data = data.get('search', {})
            vector_data = data.get('vector', {})
            pagination_data = data.get('pagination', {})
            
            # 验证向量参数
            if not vector_data.get('query_vector'):
                return error_response(400, "查询向量不能为空")
            
            # 构建查询参数
            search_params = SearchParams(
                query="",  # 知识库检索不需要query字段
                search_words=search_data.get('search_words'),
                content_biz_labels=search_data.get('content_biz_labels'),
                content_entity_labels=search_data.get('content_entity_labels')
            )
            
            vector_params = VectorParams(
                query_vector=vector_data['query_vector'],
                k=vector_data.get('k', 50),
                num_candidates=vector_data.get('num_candidates', 100),
                boost=vector_data.get('boost', 3.0)
            )
            
            pagination_params = PaginationParams(
                from_offset=pagination_data.get('from_offset', 0),
                size=pagination_data.get('size', 20)
            )
            
            # 构建查询
            query_builder = KnowledgeQueryBuilder()
            query = query_builder.build_knowledge_query(
                group_id, year, search_params, vector_params, pagination_params
            )
            
            # 执行搜索
            result = es_service.search(index_name, query)
            
            # 格式化响应
            response_data = {
                'total': result.total,
                'max_score': result.max_score,
                'hits': [
                    {
                        'id': hit.id,
                        'score': hit.score,
                        'chunck_id': hit.source.get('chunck_id'),
                        'entity_id': hit.source.get('entity_id')
                    }
                    for hit in result.hits
                ],
                'took': result.took
            }
            
            return success_response(response_data, "知识库检索成功")
            
        except ValueError as e:
            return error_response(400, str(e))
        except Exception as e:
            current_app.logger.error(f"知识库检索失败: {str(e)}", exc_info=True)
            return error_response(500, "检索服务异常", str(e))


@ns.route('/chunks/<group_id>/<year>')
class ChunkDetail(Resource):
    """获取文段详细内容"""
    
    @ns.doc('get_chunk_details')
    @ns.param('group_id', '组织ID')
    @ns.param('year', '年份')
    @ns.param('chunk_ids', '文段ID列表，逗号分隔')
    # @ns.marshal_list_with(chunk_detail_model)
    def get(self, group_id: str, year: str):
        """
        根据文段ID获取详细内容
        
        从chunk索引中获取文段的原始内容
        """
        try:
            # 验证参数
            #validate_group_year(group_id, year)
            
            chunk_ids = request.args.get('chunk_ids', '')
            if not chunk_ids:
                return error_response(400, "chunk_ids 参数不能为空")
            
            chunk_id_list = [cid.strip() for cid in chunk_ids.split(',') if cid.strip()]
            if not chunk_id_list:
                return error_response(400, "chunk_ids 格式错误")
            
            # 构建chunk索引名称
            chunk_index_name = f"{current_app.config['ES_CHUNK_INDEX_PREFIX']}_{group_id}_{year}"
            
            # 检查索引是否存在
            if not es_service.index_exists(chunk_index_name):
                return error_response(404, f"文段索引 {chunk_index_name} 不存在")
            
            # 批量获取文档
            results = []
            for chunk_id in chunk_id_list:
                doc = es_service.get_document(chunk_index_name, chunk_id)
                if doc:
                    results.append({
                        'chunck_id': chunk_id,
                        'content': doc.get('content', ''),
                        'entity_id': doc.get('entity_id', '')
                    })
            
            return success_response(results, f"成功获取 {len(results)} 个文段详情")
            
        except ValueError as e:
            return error_response(400, str(e))
        except Exception as e:
            current_app.logger.error(f"获取文段详情失败: {str(e)}", exc_info=True)
            return error_response(500, "服务异常", str(e))
