"""
Flask 应用工厂函数
"""
import logging
import os
from flask import Flask
from flask_restx import Api
from config.settings import config


def create_app(config_name=None):
    """创建 Flask 应用实例"""
    
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 配置日志
    setup_logging(app)
    
    # 初始化 API
    api = Api(
        app,
        version='1.0',
        title='ES 检索服务 API',
        description='''
        基于 Elasticsearch 的语义检索和知识库检索服务

        ## 功能模块

        ### 语义检索 (/api/v1/semantic)
        - **在线课程检索**: 支持课程名称、教师姓名、ASR数据等多字段匹配和向量检索
        - **辅学资源检索**: 支持资源名称、内容关键字等检索
        - **图谱资源检索**: 支持图谱名称、业务关键词等检索

        ### 知识库检索 (/api/v1/knowledge)
        - **知识库检索**: 基于知识点片段的向量检索和关键词匹配
        - **文段详情获取**: 根据文段ID获取原始内容

        ### AI 问答检索 (/api/v1/qa)
        - **AI 课程问答**: 基于特定课程知识库的问答检索
        - **图谱智能问答**: 基于特定图谱知识库的问答检索

        ## 索引命名规则
        - 主索引: `resource_entity_global_{group_id}_{year}`
        - 文段索引: `resource_entity_global_chunk_{group_id}_{year}`

        ## 认证说明
        当前版本暂不需要认证，生产环境建议添加 API Key 认证。
        ''',
        doc='/docs/' if app.config['SWAGGER_ENABLED'] else False,
        prefix='/api/v1',
        authorizations={
            'apikey': {
                'type': 'apiKey',
                'in': 'header',
                'name': 'X-API-Key',
                'description': 'API Key 认证（当前版本可选）'
            }
        },
        security='apikey'
    )
    
    # 注册蓝图
    register_blueprints(api)
    
    # 注册错误处理器
    register_error_handlers(app)

    # 添加健康检查接口
    register_health_check(app)

    return app


def setup_logging(app):
    """配置日志"""
    import os
    from logging.handlers import RotatingFileHandler

    # 确保日志目录存在
    log_file = app.config.get('LOG_FILE')
    if log_file:
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

    # 配置日志级别
    log_level = getattr(logging, app.config.get('LOG_LEVEL', 'INFO'))
    app.logger.setLevel(log_level)

    # 配置文件日志处理器
    if log_file and not app.debug:
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=app.config.get('LOG_MAX_BYTES', 10485760),
            backupCount=app.config.get('LOG_BACKUP_COUNT', 5)
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(log_level)
        app.logger.addHandler(file_handler)

    # 配置控制台日志处理器
    if app.debug:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s'
        ))
        console_handler.setLevel(log_level)
        app.logger.addHandler(console_handler)

    app.logger.info('ES 检索服务启动')


def register_blueprints(api):
    """注册 API 蓝图"""
    from app.api.semantic_search import ns as semantic_ns
    from app.api.knowledge_search import ns as knowledge_ns
    from app.api.qa_search import ns as qa_ns
    
    api.add_namespace(semantic_ns, path='/semantic')
    api.add_namespace(knowledge_ns, path='/knowledge')
    api.add_namespace(qa_ns, path='/qa')


def register_error_handlers(app):
    """注册错误处理器"""
    from app.utils.response import error_response
    
    @app.errorhandler(400)
    def bad_request(error):
        return error_response(400, '请求参数错误', str(error))
    
    @app.errorhandler(404)
    def not_found(error):
        return error_response(404, '资源未找到', str(error))
    
    @app.errorhandler(500)
    def internal_error(error):
        return error_response(500, '服务器内部错误', str(error))
    
    @app.errorhandler(Exception)
    def handle_exception(error):
        app.logger.error(f'未处理的异常: {str(error)}', exc_info=True)
        return error_response(500, '服务器内部错误', '请联系管理员')


def register_health_check(app):
    """注册健康检查接口"""
    import datetime
    from app.utils.response import success_response, error_response

    @app.route('/health')
    def health_check():
        """健康检查接口"""
        try:
            # 检查 ES 连接
            from app.services.es_service import es_service
            es_status = es_service.health_check()

            return success_response({
                'status': 'healthy',
                'elasticsearch': 'connected' if es_status else 'disconnected',
                'timestamp': datetime.datetime.now().isoformat(),
                'version': '1.0.0',
                'swagger_enabled': app.config.get('SWAGGER_ENABLED', False)
            }, '服务运行正常')
        except Exception as e:
            app.logger.error(f'健康检查失败: {str(e)}', exc_info=True)
            return error_response(503, '服务不可用', str(e))
