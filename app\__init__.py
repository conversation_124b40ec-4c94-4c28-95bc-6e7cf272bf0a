"""
Flask 应用工厂函数
"""
import logging
import os
from flask import Flask
from flask_restx import Api
from config.settings import config


def create_app(config_name=None):
    """创建 Flask 应用实例"""
    
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 配置日志
    setup_logging(app)
    
    # 初始化 API
    api = Api(
        app,
        version='1.0',
        title='ES 检索服务 API',
        description='基于 Elasticsearch 的语义检索和知识库检索服务',
        doc='/docs/' if app.config['SWAGGER_ENABLED'] else False,
        prefix='/api/v1'
    )
    
    # 注册蓝图
    register_blueprints(api)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    return app


def setup_logging(app):
    """配置日志"""
    if not app.debug and not app.testing:
        if app.config.get('LOG_FILE'):
            file_handler = logging.FileHandler(app.config['LOG_FILE'])
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
            app.logger.addHandler(file_handler)
        
        app.logger.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
        app.logger.info('ES 检索服务启动')


def register_blueprints(api):
    """注册 API 蓝图"""
    from app.api.semantic_search import ns as semantic_ns
    from app.api.knowledge_search import ns as knowledge_ns
    from app.api.qa_search import ns as qa_ns
    
    api.add_namespace(semantic_ns, path='/semantic')
    api.add_namespace(knowledge_ns, path='/knowledge')
    api.add_namespace(qa_ns, path='/qa')


def register_error_handlers(app):
    """注册错误处理器"""
    from app.utils.response import error_response
    
    @app.errorhandler(400)
    def bad_request(error):
        return error_response(400, '请求参数错误', str(error))
    
    @app.errorhandler(404)
    def not_found(error):
        return error_response(404, '资源未找到', str(error))
    
    @app.errorhandler(500)
    def internal_error(error):
        return error_response(500, '服务器内部错误', str(error))
    
    @app.errorhandler(Exception)
    def handle_exception(error):
        app.logger.error(f'未处理的异常: {str(error)}', exc_info=True)
        return error_response(500, '服务器内部错误', '请联系管理员')
