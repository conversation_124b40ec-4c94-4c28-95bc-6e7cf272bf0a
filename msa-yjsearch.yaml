apiVersion: v1
kind: Service
metadata:
  name: yjsearch
  namespace: edubase
  labels:
    app: yjsearch
spec:
  ports:
  - name: http
    port: 80
    protocol: TCP
  selector:
    app: yjsearch

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: yjsearch
  namespace: edubase
spec:
  selector:
    matchLabels:
      app: yjsearch
  replicas: 1
  template:
    metadata:
      labels:
        app: yjsearch
        version: v1
    spec:
      imagePullSecrets:
      - name: cmcregsecret
      containers:
      - name: yjsearch
        image: cmc-tcr.tencentcloudcr.com/yunjiao/msa-yjsearch:develop
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 5
          httpGet:
            path: /healthy
            port: 80
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 30
          successThreshold: 1
          timeoutSeconds: 5
        resources: # 资源限制
          limits:
            cpu: 1000m
            memory: 2000Mi
          requests:
            cpu: 50m
            memory: 50Mi
        env:
        - name: YJSEARCH_CFG_API
          value: "http://yjsearch/"
        - name: YJ<PERSON><PERSON>CH_CFG_APPID
          value: "**********"
        - name: YJ<PERSON>ARCH_CFG_CMCKONGURL
          value: "http://api.chinamcloud.com/"
        - name: YJSEARCH_CFG_CRON
          value: "http://yjsearchcron/v1"
        - name: YJSEARCH_CFG_DBHOST
          value: "yunjiao-mysql00:3306"
        - name: YJSEARCH_CFG_DBNAME
          value: "yj_jyapp"
        - name: YJSEARCH_CFG_DBPASSWORD
          value: "hqy-webtv"
        - name: YJSEARCH_CFG_DBUSER
          value: "root"
        - name: YJSEARCH_CFG_EXPIRED
          value: "86400"
        - name: YJSEARCH_CFG_FLYPAGE
          value: "https://flypage.chinamcloud.com/api/page-api/api"
        - name: YJSEARCH_CFG_HTTP_DIALTIMEOUT
          value: "60s"
        - name: YJSEARCH_CFG_HTTP_REQUESTTIMEOUT
          value: "90s"
        - name: YJSEARCH_CFG_HTTP_TLSHANDSHAKETIMEOUT
          value: "10s"
        - name: YJSEARCH_CFG_IDENTIFIER
          value: "administrator"
        - name: YJSEARCH_CFG_LIVEBIZID
          value: "90660"
        - name: YJSEARCH_CFG_LIVESECRETID
          value: "AKIDypYAbrxVt7C9ya5EH2d3LTP9hFbvi7zf"
        - name: YJSEARCH_CFG_LIVESECRETKEY
          value: "gBZmW8arwoW3PZZTDuCRWQAj6JLAuUs4"
        - name: YJSEARCH_CFG_PEOPLE
          value: "200"
        - name: YJSEARCH_CFG_PGCURL
          value: "http://flyapp-api.chinamcloud.com/interface/api"
        - name: YJSEARCH_CFG_PGC_GROUPCODE
          value: "1800000001"
        - name: YJSEARCH_CFG_PGC_GROUPID
          value: "c10e87f39873512a16727e17f57456a5"
        - name: YJSEARCH_CFG_PGC_LOGINNAME
          value: "huaqiyun"
        - name: YJSEARCH_CFG_PGC_PASSWORD
          value: "654321hqy"
        - name: YJSEARCH_CFG_PGC_SECRETKEY
          value: "I8x1Hu4wO9aEn7M2d"
        - name: YJSEARCH_CFG_PORT
          value: "80"
        - name: YJSEARCH_CFG_REDISADD
          value: "yunjiao-redis00:6379"
        - name: YJSEARCH_CFG_REDISPW
          value: "Mcloud2021"
        - name: YJSEARCH_CFG_REDISDB
          value: "31"
        - name: YJSEARCH_CFG_SECRETID
          value: "AKIDO4aw76aFesjmtwK0mMl164sNhuCKolFX"
        - name: YJSEARCH_CFG_SECRETKEY
          value: "2zlPuFMI4MUSWSAxKYgkfRBkePzPfrsM"
        - name: YJSEARCH_CFG_SOURCE_MOUNTPATH
          value: "./source"
        - name: YJSEARCH_CFG_TENANTURL
          value: "http://tenantapi.edubase/"
        - name: YJSEARCH_CFG_CACHEEXPIRE_PGCPAGE
          value: "3600h"
        - name: YJSEARCH_CFG_LIVEINFO_HTTPRETRY
          value: "1"
        - name: YJSEARCH_CFG_DINGTALK_MEMBER_DSN
          value: "root:hqy-webtv@tcp(yunjiao-mysql00:3306)/yj_message?charset=utf8"
        volumeMounts:
        - name: localtime
          mountPath: /etc/localtime
        ports:
        - containerPort: 80
      volumes:
      - name: localtime
        hostPath:
          path: /etc/localtime