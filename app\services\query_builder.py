"""
Elasticsearch 查询构建器
"""
from typing import Dict, Any, List, Optional
from app.models.es_models import (
    FilterParams, SearchParams, VectorParams, 
    PaginationParams, SortParams, HighlightParams
)


class QueryBuilder:
    """ES 查询构建器"""
    
    def __init__(self):
        self.query = {"query": {"bool": {}}}
    
    def add_filter(self, filter_params: FilterParams) -> 'QueryBuilder':
        """添加过滤条件"""
        filters = []
        
        # 实体类型过滤
        if filter_params.entity_type is not None:
            filters.append({"term": {"entity_type": filter_params.entity_type}})
        
        # 分类过滤
        if filter_params.category is not None:
            filters.append({"term": {"category": filter_params.category}})
        
        # 学院代码过滤
        if filter_params.kkxy_code:
            filters.append({"term": {"entity_meta_data.kkxy_code": filter_params.kkxy_code}})
        
        # 学期ID过滤
        if filter_params.term_ids:
            filters.append({"terms": {"entity_meta_data.term_ids": filter_params.term_ids}})
        
        # 学科过滤
        if filter_params.xueke:
            filters.append({"term": {"entity_meta_data.xueke": filter_params.xueke}})
        
        # 时间范围过滤
        if filter_params.start_time or filter_params.end_time:
            time_filter = {"range": {"entity_meta_data.add_time": {}}}
            if filter_params.start_time:
                time_filter["range"]["entity_meta_data.add_time"]["gte"] = filter_params.start_time
            if filter_params.end_time:
                time_filter["range"]["entity_meta_data.add_time"]["lte"] = filter_params.end_time
            filters.append(time_filter)
        
        # 知识库ID过滤
        if filter_params.kg_ids:
            filters.append({"terms": {"kg_ids": filter_params.kg_ids}})
        
        if filters:
            self.query["query"]["bool"]["filter"] = filters
        
        return self
    
    def add_search(self, search_params: SearchParams) -> 'QueryBuilder':
        """添加搜索条件"""
        should_queries = []
        
        # 多字段匹配查询
        if search_params.query:
            multi_match = {
                "multi_match": {
                    "query": search_params.query,
                    "fields": [
                        "title^1", "realname^1", "college_name^1", 
                        "subject_name^1", "major_name^1", "content_biz_labels^1"
                    ],
                    "type": "phrase",
                    "analyzer": "ik_smart"
                }
            }
            should_queries.append(multi_match)
            
            # ASR 数据匹配
            asr_match = {
                "match": {
                    "asr_data": {
                        "query": search_params.query,
                        "boost": 2,
                        "analyzer": "ik_smart"
                    }
                }
            }
            should_queries.append(asr_match)
        
        # 搜索关键词匹配
        if search_params.search_words:
            should_queries.append({
                "terms": {
                    "search_words": search_params.search_words,
                    "boost": 1
                }
            })
        
        # 业务关键词匹配
        if search_params.content_biz_labels:
            should_queries.append({
                "terms": {
                    "content_biz_labels": search_params.content_biz_labels,
                    "boost": 1
                }
            })
        
        # 实体标签匹配
        if search_params.content_entity_labels:
            should_queries.append({
                "terms": {
                    "content_entity_labels": search_params.content_entity_labels,
                    "boost": 1
                }
            })
        
        if should_queries:
            self.query["query"]["bool"]["should"] = should_queries
            self.query["query"]["bool"]["minimum_should_match"] = 1
        
        return self
    
    def add_vector_search(self, vector_params: VectorParams) -> 'QueryBuilder':
        """添加向量搜索"""
        if vector_params.query_vector:
            # 如果已有 should 查询，添加 KNN 查询
            if "should" in self.query["query"]["bool"]:
                knn_query = {
                    "knn": {
                        "field": "text_segment_embedding",
                        "query_vector": vector_params.query_vector,
                        "k": vector_params.k,
                        "num_candidates": vector_params.num_candidates,
                        "boost": vector_params.boost
                    }
                }
                self.query["query"]["bool"]["should"].append(knn_query)
            else:
                # 独立的 KNN 查询
                self.query["knn"] = [{
                    "field": "text_segment_embedding",
                    "query_vector": vector_params.query_vector,
                    "k": vector_params.k,
                    "num_candidates": vector_params.num_candidates,
                    "boost": vector_params.boost
                }]
        
        return self
    
    def add_pagination(self, pagination_params: PaginationParams) -> 'QueryBuilder':
        """添加分页"""
        self.query["from"] = pagination_params.from_offset
        self.query["size"] = pagination_params.size
        return self
    
    def add_sort(self, sort_params: SortParams) -> 'QueryBuilder':
        """添加排序"""
        self.query["sort"] = [{sort_params.field: {"order": sort_params.order}}]
        return self
    
    def add_highlight(self, highlight_params: HighlightParams) -> 'QueryBuilder':
        """添加高亮"""
        highlight_config = {
            "fields": {field: {} for field in highlight_params.fields},
            "pre_tags": highlight_params.pre_tags,
            "post_tags": highlight_params.post_tags
        }
        self.query["highlight"] = highlight_config
        return self
    
    def add_aggregation(self, agg_name: str, agg_config: Dict[str, Any]) -> 'QueryBuilder':
        """添加聚合"""
        if "aggs" not in self.query:
            self.query["aggs"] = {}
        self.query["aggs"][agg_name] = agg_config
        return self
    
    def add_source_fields(self, fields: List[str]) -> 'QueryBuilder':
        """指定返回字段"""
        self.query["_source"] = fields
        return self
    
    def build(self) -> Dict[str, Any]:
        """构建最终查询"""
        return self.query
    
    def reset(self) -> 'QueryBuilder':
        """重置查询构建器"""
        self.query = {"query": {"bool": {}}}
        return self


class SemanticQueryBuilder(QueryBuilder):
    """语义检索查询构建器"""
    
    def build_course_query(self, group_id: str, year: str, filter_params: FilterParams, 
                          search_params: SearchParams, vector_params: Optional[VectorParams] = None,
                          pagination_params: Optional[PaginationParams] = None,
                          highlight_params: Optional[HighlightParams] = None) -> Dict[str, Any]:
        """构建在线课程查询"""
        # 设置默认过滤条件
        filter_params.entity_type = 0
        filter_params.category = 1
        
        self.add_filter(filter_params)
        self.add_search(search_params)
        
        if vector_params:
            self.add_vector_search(vector_params)
        
        if pagination_params:
            self.add_pagination(pagination_params)
        else:
            self.add_pagination(PaginationParams())
        
        if highlight_params:
            self.add_highlight(highlight_params)
        else:
            self.add_highlight(HighlightParams())
        
        # 添加课程维度聚合
        self.add_aggregation("group_agg", {
            "terms": {
                "field": "parent_group_id",
                "size": 10
            }
        })
        
        # 添加默认排序
        self.add_sort(SortParams())
        
        return self.build()


class KnowledgeQueryBuilder(QueryBuilder):
    """知识库检索查询构建器"""
    
    def build_knowledge_query(self, group_id: str, year: str, 
                             search_params: SearchParams, vector_params: VectorParams,
                             pagination_params: Optional[PaginationParams] = None) -> Dict[str, Any]:
        """构建知识库查询"""
        # 设置知识点片段过滤
        filter_params = FilterParams(entity_type=1)
        self.add_filter(filter_params)
        
        # 添加搜索条件（minimum_should_match=0）
        if search_params.search_words or search_params.content_biz_labels or search_params.content_entity_labels:
            should_queries = []
            
            if search_params.search_words:
                should_queries.append({
                    "terms": {"search_words": search_params.search_words, "boost": 1}
                })
            
            if search_params.content_biz_labels:
                should_queries.append({
                    "terms": {"content_biz_labels": search_params.content_biz_labels, "boost": 1}
                })
            
            if search_params.content_entity_labels:
                should_queries.append({
                    "terms": {"content_entity_labels": search_params.content_entity_labels, "boost": 1}
                })
            
            if should_queries:
                self.query["query"]["bool"]["should"] = should_queries
                self.query["query"]["bool"]["minimum_should_match"] = 0
        
        # 添加向量搜索
        self.add_vector_search(vector_params)
        
        if pagination_params:
            self.add_pagination(pagination_params)
        else:
            self.add_pagination(PaginationParams(size=20))
        
        # 指定返回字段
        self.add_source_fields(["chunck_id", "entity_id"])
        
        # 添加默认排序
        self.add_sort(SortParams())
        
        return self.build()
