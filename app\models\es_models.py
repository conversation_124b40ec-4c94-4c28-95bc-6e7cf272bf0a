"""
Elasticsearch 数据模型定义
"""
from dataclasses import dataclass
from typing import List, Optional, Dict, Any


@dataclass
class SearchResult:
    """搜索结果数据模型"""
    id: str
    score: float
    source: Dict[str, Any]
    highlight: Optional[Dict[str, List[str]]] = None
    
    @classmethod
    def from_es_hit(cls, hit: Dict[str, Any]) -> 'SearchResult':
        """从 ES 查询结果创建 SearchResult 实例"""
        return cls(
            id=hit['_id'],
            score=hit['_score'],
            source=hit['_source'],
            highlight=hit.get('highlight')
        )


@dataclass
class SearchResponse:
    """搜索响应数据模型"""
    total: int
    max_score: float
    hits: List[SearchResult]
    aggregations: Optional[Dict[str, Any]] = None
    took: Optional[int] = None
    
    @classmethod
    def from_es_response(cls, response: Dict[str, Any]) -> 'SearchResponse':
        """从 ES 响应创建 SearchResponse 实例"""
        hits_data = response['hits']
        hits = [SearchResult.from_es_hit(hit) for hit in hits_data['hits']]
        
        # 处理 total 字段（ES 7.x+ 格式）
        total = hits_data['total']
        if isinstance(total, dict):
            total_count = total['value']
        else:
            total_count = total
        
        return cls(
            total=total_count,
            max_score=hits_data.get('max_score', 0.0),
            hits=hits,
            aggregations=response.get('aggregations'),
            took=response.get('took')
        )


@dataclass
class IndexConfig:
    """索引配置模型"""
    name: str
    group_id: str
    year: str
    
    @property
    def full_name(self) -> str:
        """获取完整索引名称"""
        return f"{self.name}_{self.group_id}_{self.year}"


@dataclass
class FilterParams:
    """过滤参数模型"""
    entity_type: Optional[int] = None
    category: Optional[int] = None
    kkxy_code: Optional[str] = None
    term_ids: Optional[List[str]] = None
    xueke: Optional[str] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    kg_ids: Optional[List[str]] = None


@dataclass
class SearchParams:
    """搜索参数模型"""
    query: str
    search_words: Optional[List[str]] = None
    content_biz_labels: Optional[List[str]] = None
    content_entity_labels: Optional[List[str]] = None


@dataclass
class VectorParams:
    """向量搜索参数模型"""
    query_vector: List[float]
    k: int = 50
    num_candidates: int = 100
    boost: float = 3.0


@dataclass
class PaginationParams:
    """分页参数模型"""
    from_offset: int = 0
    size: int = 10


@dataclass
class SortParams:
    """排序参数模型"""
    field: str = "_score"
    order: str = "desc"


@dataclass
class HighlightParams:
    """高亮参数模型"""
    fields: List[str] = None
    pre_tags: List[str] = None
    post_tags: List[str] = None
    
    def __post_init__(self):
        if self.fields is None:
            self.fields = ["title", "realname", "asr_data"]
        if self.pre_tags is None:
            self.pre_tags = ["<em>"]
        if self.post_tags is None:
            self.post_tags = ["</em>"]
