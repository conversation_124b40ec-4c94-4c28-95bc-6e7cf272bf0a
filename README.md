# ES 检索服务

基于 Flask 和 Elasticsearch 的语义检索和知识库检索服务。

## 功能特性

- 语义检索接口（在线课程、辅学资源、图谱资源）
- 知识库检索接口
- AI 课程问答和图谱智能问答
- 支持向量检索（KNN）
- 完整的 Swagger API 文档
- 统一的错误处理和响应格式
- 可配置的环境变量

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env` 并修改配置：

```bash
cp .env.example .env
```

### 3. 启动服务

```bash
python run.py
```

### 4. 访问 API 文档

访问 `http://localhost:5000/docs/` 查看 Swagger API 文档。

## API 接口

### 语义检索接口

- `POST /api/v1/semantic/courses` - 在线课程检索
- `POST /api/v1/semantic/resources` - 辅学资源检索  
- `POST /api/v1/semantic/graphs` - 图谱资源检索

### 知识库检索接口

- `POST /api/v1/knowledge/search` - 知识库检索

### 问答接口

- `POST /api/v1/qa/course` - AI 课程问答
- `POST /api/v1/qa/graph` - 图谱智能问答

## 环境变量配置

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| ES_HOST | Elasticsearch 主机地址 | localhost |
| ES_PORT | Elasticsearch 端口 | 9200 |
| ES_USERNAME | Elasticsearch 用户名 | - |
| ES_PASSWORD | Elasticsearch 密码 | - |
| SWAGGER_ENABLED | 是否启用 Swagger 文档 | True |

更多配置项请参考 `.env.example` 文件。

## 项目结构

```
yjsearch/
├── app/                    # 应用主目录
│   ├── api/               # API 接口
│   ├── models/            # 数据模型
│   ├── services/          # 业务逻辑
│   └── utils/             # 工具函数
├── config/                # 配置文件
├── requirements.txt       # 依赖包
├── run.py                # 启动文件
└── README.md             # 说明文档
```
