image: golang:alpine

stages:
  - build
  - deploy

# ===== develop =====

job-config_build_develop:
  image: docker
  stage: build
  tags:
    - develop
  only:
    - develop
  script:
    - pwd
    - docker login ccr.ccs.tencentyun.com --username=100008993117 --password=Pass2word
    - docker login cmc-tcr.tencentcloudcr.com --username 100008433169 --password ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    - docker buildx build . -t ccr.ccs.tencentyun.com/yunjiao/msa-yjsearch:develop --platform=linux/arm64,linux/amd64 --push 
    - docker buildx build . -t cmc-tcr.tencentcloudcr.com/yunjiao/msa-yjsearch:develop --platform=linux/arm64,linux/amd64 --push 

job-config_deploy_develop:
  image: ccr.ccs.tencentyun.com/yunjiao/k8s-kubectl:yjdev
  stage: deploy
  tags:
    - yjdev
  only:
    - develop
  script:
    - kubectl get Deployment yjsearch -n eduliving && kubectl delete Deployment yjsearch -n eduliving
    - kubectl apply -f msa-yjsearch.yaml
