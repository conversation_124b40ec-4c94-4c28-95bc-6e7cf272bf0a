"""
统一响应格式处理
"""
from flask import jsonify
from typing import Any, Dict, Optional


def success_response(data: Any = None, message: str = "成功", code: int = 200) -> Dict:
    """成功响应格式"""
    response = {
        "code": code,
        "message": message,
        "success": True
    }
    
    if data is not None:
        response["data"] = data
    
    # return jsonify(response), code
    return response, code


def error_response(code: int = 400, message: str = "请求失败", details: Optional[str] = None) -> Dict:
    """错误响应格式"""
    response = {
        "code": code,
        "message": message,
        "success": False
    }
    
    if details:
        response["details"] = details
    
    # return jsonify(response), code
    return response, code


def paginated_response(data: list, total: int, page: int, page_size: int, message: str = "成功") -> Dict:
    """分页响应格式"""
    response_data = {
        "items": data,
        "pagination": {
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        }
    }
    
    return success_response(response_data, message)
