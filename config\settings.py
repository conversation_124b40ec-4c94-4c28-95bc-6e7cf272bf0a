"""
应用配置文件
包含 Elasticsearch 连接配置和其他应用设置
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class Config:
    """基础配置类"""
    
    # Flask 配置
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    
    # Elasticsearch 配置
    ES_HOST = os.getenv('ES_HOST', 'localhost')
    ES_PORT = int(os.getenv('ES_PORT', 9200))
    ES_USERNAME = os.getenv('ES_USERNAME', '')
    ES_PASSWORD = os.getenv('ES_PASSWORD', '')
    ES_USE_SSL = os.getenv('ES_USE_SSL', 'False').lower() == 'true'
    ES_VERIFY_CERTS = os.getenv('ES_VERIFY_CERTS', 'False').lower() == 'true'
    ES_TIMEOUT = int(os.getenv('ES_TIMEOUT', 30))
    
    # 索引配置
    ES_INDEX_PREFIX = os.getenv('ES_INDEX_PREFIX', 'resource_entity_global')
    ES_CHUNK_INDEX_PREFIX = os.getenv('ES_CHUNK_INDEX_PREFIX', 'resource_entity_global_chunk')
    
    # Swagger 文档配置
    SWAGGER_ENABLED = os.getenv('SWAGGER_ENABLED', 'True').lower() == 'true'
    SWAGGER_UI_DOC_EXPANSION = 'list'
    SWAGGER_UI_OPERATION_ID = True
    SWAGGER_UI_REQUEST_DURATION = True
    
    # 分页配置
    DEFAULT_PAGE_SIZE = int(os.getenv('DEFAULT_PAGE_SIZE', 10))
    MAX_PAGE_SIZE = int(os.getenv('MAX_PAGE_SIZE', 100))
    
    # 向量检索配置
    DEFAULT_KNN_K = int(os.getenv('DEFAULT_KNN_K', 50))
    DEFAULT_KNN_NUM_CANDIDATES = int(os.getenv('DEFAULT_KNN_NUM_CANDIDATES', 100))
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'app.log')


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False


class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True


# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
