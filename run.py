#!/usr/bin/env python3
"""
ES 检索服务启动脚本
"""
import os
import sys
from app import create_app

def main():
    """主函数"""
    # 设置环境变量
    os.environ.setdefault('FLASK_ENV', 'development')

    # 创建应用
    app = create_app()

    # 获取配置
    host = os.getenv('FLASK_HOST', '0.0.0.0')
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'

    print(f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    ES 检索服务启动                            ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  服务地址: http://{host}:{port}                              ║
    ║  API 文档: http://{host}:{port}/docs/                       ║
    ║  健康检查: http://{host}:{port}/health                       ║
    ║  调试模式: {'开启' if debug else '关闭'}                      ║
    ║  Swagger: {'开启' if app.config.get('SWAGGER_ENABLED') else '关闭'}  ║
    ╚══════════════════════════════════════════════════════════════╝
    """)

    try:
        # 启动服务
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
