# Flask 配置
SECRET_KEY=your-secret-key-here
DEBUG=True
FLASK_ENV=development
HOST=0.0.0.0
PORT=5000

# Elasticsearch 配置
ES_HOST=localhost
ES_PORT=9200
ES_USERNAME=
ES_PASSWORD=
ES_USE_SSL=False
ES_VERIFY_CERTS=False
ES_TIMEOUT=30

# 索引配置
ES_INDEX_PREFIX=resource_entity_global
ES_CHUNK_INDEX_PREFIX=resource_entity_global_chunk

# Swagger 文档配置
SWAGGER_ENABLED=True

# 分页配置
DEFAULT_PAGE_SIZE=10
MAX_PAGE_SIZE=100

# 向量检索配置
DEFAULT_KNN_K=50
DEFAULT_KNN_NUM_CANDIDATES=100

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=app.log
